import { ArrowLeft, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { Spinner } from '@/components/common/Loader';
import { Input } from '@/components/ui';
import { useAuth } from '@/context/AuthContext';
import { useTenant } from '@/context/TenantContext';

export const ChangeEmail: React.FC = () => {
  const { user } = useAuth();
  const { setActiveAgent } = useTenant();
  const currentEmail = user?.email || '';
  const navigate = useNavigate();
  const [step, setStep] = useState<'verification' | 'form'>('verification');
  const [newEmail, setNewEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState([
    '',
    '',
    '',
    '',
    '',
    '',
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chatMessage, setChatMessage] = useState<string>('');
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Set active agent to Regis when component mounts
  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent]);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // TODO: Implement email change request
      console.log('Changing email to:', newEmail);
      // Navigate back to profile after success
      navigate('/dashboard/settings/profile');
    } catch (err) {
      setError('Failed to change email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCodes = [...verificationCode];
      newCodes[index] = value;
      setVerificationCode(newCodes);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const code = verificationCode.join('');
    if (code.length !== 6) {
      setError('Please enter all 6 digits');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // TODO: Implement verification
      console.log('Verifying code:', code);
      setStep('form');
    } catch (err) {
      setError('Invalid verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement resend logic
      console.log('Resending verification code');
    } catch (err) {
      setError('Failed to resend code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (step === 'verification') {
    return (
      <div className="flex h-full">
        {/* Chat Sidebar - LEFT SIDE */}
        <EnhancedChatSidebar
          reloadChatHistoryRef={reloadChatHistoryRef}
          externalInjectedMessage={chatMessage}
        />

        {/* Main Content - RIGHT SIDE */}
        <div className="flex-1 overflow-y-auto">
          <div className="flex w-full max-w-[850px] flex-col gap-4 p-8">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/dashboard/settings/profile')}
                className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-gray-100"
              >
                <ArrowLeft className="h-4 w-4" />
              </button>
              <h1 className="text-2xl font-semibold text-blackOne">
                Email Confirmation
              </h1>
            </div>

            {error && (
              <div className="relative overflow-hidden rounded-lg border border-[#fecaca] bg-gradient-to-r from-[#fef2f2] to-[#fee2e2] p-4 shadow-sm">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-[#dc2626]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-[#991b1b]">
                      {error}
                    </p>
                  </div>
                  <button
                    onClick={() => setError(null)}
                    className="flex-shrink-0 text-[#f87171] transition-colors hover:text-[#dc2626]"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}

            <div className="max-w-md">
              <p className="mb-6 text-sm text-subText">
                For security, please confirm it's you by inputting the 6 digit
                code sent to your email before we continue.
              </p>

              <form onSubmit={handleVerificationSubmit} className="space-y-6">
                <div>
                  <label className="mb-4 block text-sm font-medium text-blackOne">
                    Enter a 6-digit code to sent to your email to verify your
                    identity
                  </label>
                  <div className="flex gap-3">
                    {verificationCode.map((code, index) => (
                      <input
                        key={index}
                        id={`code-${index}`}
                        type="text"
                        maxLength={1}
                        value={code}
                        onChange={e =>
                          handleVerificationCodeChange(index, e.target.value)
                        }
                        className="h-12 w-12 rounded-md border border-[#DFEAF2] text-center text-lg font-medium focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                        disabled={isLoading}
                      />
                    ))}
                  </div>
                </div>

                <div className="text-sm text-subText">
                  Didn't get a message?{' '}
                  <button
                    type="button"
                    onClick={handleResendCode}
                    disabled={isLoading}
                    className="text-primary hover:underline disabled:opacity-50"
                  >
                    Resend Code
                  </button>
                </div>

                <button
                  type="submit"
                  disabled={isLoading || verificationCode.some(code => !code)}
                  className="w-full rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <Spinner className="h-4 w-4" />
                    </div>
                  ) : (
                    'Proceed'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalInjectedMessage={chatMessage}
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex w-full max-w-[850px] flex-col gap-4 p-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setStep('verification')}
              className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-gray-100"
            >
              <ArrowLeft className="h-4 w-4" />
            </button>
            <h1 className="text-2xl font-semibold text-blackOne">
              Change Email
            </h1>
          </div>

          {error && (
            <div className="relative overflow-hidden rounded-lg border border-[#fecaca] bg-gradient-to-r from-[#fef2f2] to-[#fee2e2] p-4 shadow-sm">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-[#dc2626]"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#991b1b]">{error}</p>
                </div>
                <button
                  onClick={() => setError(null)}
                  className="flex-shrink-0 text-[#f87171] transition-colors hover:text-[#dc2626]"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}

          <div className="max-w-md">
            <form onSubmit={handleEmailSubmit} className="space-y-6">
              <div>
                <label className="mb-2 block text-sm font-medium text-subText">
                  Current Email
                </label>
                <Input
                  type="email"
                  value={currentEmail}
                  disabled
                  className="h-10 w-full cursor-not-allowed rounded-md border border-[#DFEAF2] bg-gray-50 px-3 py-2 text-gray-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-subText">
                  New Email Address
                </label>
                <Input
                  type="email"
                  value={newEmail}
                  onChange={e => setNewEmail(e.target.value)}
                  placeholder="Enter new email address"
                  className="h-10 w-full rounded-md border border-[#DFEAF2] px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                  disabled={isLoading}
                />
              </div>

              <button
                type="submit"
                disabled={isLoading || !newEmail || newEmail === currentEmail}
                className="w-full rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <Spinner className="h-4 w-4" />
                  </div>
                ) : (
                  'Save Changes'
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
