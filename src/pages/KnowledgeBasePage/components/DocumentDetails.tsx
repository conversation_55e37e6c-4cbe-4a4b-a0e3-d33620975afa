import { ChevronLeft, Download, Trash2, Upload } from 'lucide-react';
import moment from 'moment';
import React, { useEffect, useState } from 'react';

import { alertIcon, fileApprove } from '@/assets/icons';
import { kbCurlLines } from '@/assets/images';
import { NotificationContainer } from '@/components/ui';
import FilePreview from '@/components/ui/FilePreview';
import { kbIcons } from '@/data/constants';
import { useNotifications } from '@/hooks/useNotifications';
import { useFileActivityLogApi } from '@/services/upivotalAgenticService';
import { AgentSuite } from '@/types/user';

import AnimatedModal from '../../../components/common/AnimatedModal';
import KnowledgeBaseFileUploadModal from './KnowledgeBaseFileUploadModal';
import { UploadLevel } from './LevelSelector';

interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  fileRef?: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  uploadedAt?: string;
  url?: string;
  size?: string;
  createdAt?: string;
}

interface DocumentDetailsProps {
  document: KnowledgeBaseDocument;
  onBack: () => void;
  onDelete?: (documentKey: string) => Promise<void>;
  onUpload?: (
    documentKey: string,
    documentName: string,
    files: File[]
  ) => Promise<void>;
  onReplace?: (
    documentKey: string,
    documentName: string,
    files: File[]
  ) => Promise<void>;
  isLoading?: boolean;
  index?: number;
  selectedAgent?: string | null;
  selectedSuite: AgentSuite;
  selectedLevel?: UploadLevel;
  allAgents: { id: string; name: string; icon: string }[];
}

interface Activity {
  action: string;
  dateModified: string;
  modifiedBy: string;
  modifiedByFirstName: string;
  modifiedByLastName: string;
}

const DocumentDetails: React.FC<DocumentDetailsProps> = ({
  document: doc,
  onBack,
  onDelete,
  onUpload,
  onReplace,
  index = 0,
  isLoading = false,
  selectedAgent,
  selectedSuite,
  selectedLevel,
  allAgents,
}) => {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [hasFile, setHasFile] = useState(doc.hasFile || false);
  const [activityLog, setActivityLog] = useState<Activity[]>([]);
  const [showAllActivities, setShowAllActivities] = useState(false);
  const [isUploadLoading, setIsUploadLoading] = useState(false);
  const [isActivityLogLoading, setIsActivityLogLoading] = useState(false);

  const activityLogApi = useFileActivityLogApi();

  const { notify, notifications, dismiss } = useNotifications();

  const fetchActivityLog = async () => {
    if (!doc.fileRef) return;

    setIsActivityLogLoading(true);
    try {
      const log = await activityLogApi(doc.fileRef);
      // Sort by dateModified in descending order (most recent first)
      const sortedActivities =
        log.data?.modifications.sort(
          (a: Activity, b: Activity) =>
            new Date(b.dateModified).getTime() -
            new Date(a.dateModified).getTime()
        ) || [];
      setActivityLog(sortedActivities);
    } catch (error) {
      console.error('Error fetching activity log:', error);
    } finally {
      setIsActivityLogLoading(false);
    }
  };

  useEffect(() => {
    fetchActivityLog();
  }, [doc.fileRef]);

  useEffect(() => {
    setHasFile(doc.hasFile || false);
  }, [doc.hasFile]);

  const handleDelete = async () => {
    if (onDelete) {
      try {
        setDeleteModalOpen(false);
        await onDelete(doc.key);
        // After successful delete, update hasFile state and refresh activity log
        setHasFile(false);
        await fetchActivityLog();
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  };

  const handleUpload = async (files: File[]) => {
    setIsUploadLoading(true);
    try {
      // Close modal immediately to show loading state
      setUploadModalOpen(false);

      // Await the upload/replace operation
      if (hasFile && doc.fileRef && onReplace) {
        await onReplace(doc.fileRef, doc.name, files);
      } else if (onUpload) {
        await onUpload(doc.key, doc.name, files);
      }

      // After successful upload, update hasFile state and refresh activity log
      setHasFile(true);
      await fetchActivityLog();

      // Show success notification
      const action = hasFile ? 'replaced' : 'uploaded';
      notify(`${doc.name} has been ${action} successfully.`);
    } catch (error) {
      console.error('Upload failed:', error);

      // Show error notification
      const action = hasFile ? 'replace' : 'upload';
      notify(`Failed to ${action} "${doc.name}". Please try again.`);
    } finally {
      setIsUploadLoading(false);
    }
  };

  const formatFileSize = (sizeInBytes: string): string => {
    const bytes = parseInt(sizeInBytes, 10);
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDownload = () => {
    if (!doc.url) {
      console.error('No document URL available');
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = doc.url;

      const downloadName =
        doc.fileName || doc.name || getFileNameFromUrl(doc.url);
      link.download = downloadName;
      link.target = '_blank'; // fallback
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
      // Fallback to opening in new tab
      window.open(doc.url, '_blank');
    }
  };

  const getFileNameFromUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      return pathname.substring(pathname.lastIndexOf('/') + 1);
    } catch {
      return 'Knowledgebase document.pdf';
    }
  };

  return (
    <div className="space-y-6">
      {/* Notifications Container */}
      <NotificationContainer
        notifications={notifications}
        onClose={dismiss}
        className="w-full"
        maxNotifications={3}
      />

      {/* Header with Back Button */}
      <div
        className="flex items-center justify-between rounded bg-blue-midnight p-6"
        style={{
          backgroundImage: `url(${kbCurlLines})`,
          backgroundSize: 'contain',
          backgroundPosition: 'right',
          backgroundRepeat: 'no-repeat',
        }}
      >
        <div className="flex items-center gap-3">
          <button
            onClick={onBack}
            className="flex h-5 w-5 items-center justify-center rounded-lg transition-colors"
          >
            <ChevronLeft className="h-5 w-5 text-white hover:text-primary" />
          </button>
          <img
            className="h-8 w-8"
            src={doc.iconUrl}
            alt={doc.name}
            onError={e => {
              (e.target as HTMLImageElement).src =
                kbIcons[index % kbIcons.length];
            }}
          />
          <h1 className="mt-0.5 font-spartan font-semibold text-white">
            {doc.name}
          </h1>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          {/* <button
            onClick={() => setDeleteModalOpen(true)}
            disabled={!hasFile || isLoading}
            className={`
            flex items-center gap-2 px-4 py-2 rounded-full border border-white transition-colors
            ${
              hasFile && !isLoading
                ? "hover:bg-red-50 hover:text-primary border-white hover:border-primary"
                : "border-grayThirteen text-grayThirteen cursor-not-allowed"
            }
          `}
          >
            <Trash2 className="h-4 w-4" />
          </button>

           <button
            onClick={handleEdit}
            disabled={!hasFile || isLoading}
            className={`
            flex items-center gap-2 px-4 py-2 rounded-full border border-grayTen transition-colors
            ${
              hasFile && !isLoading
                ? "hover:bg-red-50 hover:text-primary border-grayTen hover:border-primary"
                : "border-grayThirteen text-grayThirteen cursor-not-allowed"
            }
          `}
          >
            <PencilLine className="h-4 w-4" />
          </button> */}

          <button
            onClick={() => setUploadModalOpen(true)}
            disabled={isLoading || isUploadLoading}
            className={`
            flex items-center gap-2 rounded-full border border-white px-4 py-2 text-sm font-medium transition-colors
            ${
              isLoading || isUploadLoading
                ? 'cursor-not-allowed bg-grayTwo text-blackOne'
                : 'text-white hover:border-primary hover:bg-primary hover:text-white'
            }
          `}
          >
            {isUploadLoading ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-blackOne"></div>
                Uploading...
              </>
            ) : (
              <>
                {hasFile ? 'Replace' : 'Upload'}
                <Upload className="h-4 w-4" />
              </>
            )}
          </button>
        </div>
      </div>

      {/* File Status Section */}
      {hasFile && doc.fileName && (
        <div className="flex h-[104px] items-center gap-4">
          <div className="min-h-full w-1/2 rounded border p-4">
            <div className="mb-2 text-grayTen">Status</div>
            <div
              className={`flex w-fit items-center gap-2 rounded-full border border-grayTen bg-grayTen px-5 py-2.5 text-xs font-medium text-white`}
            >
              <img src={fileApprove} alt="file" />
              Awaiting Approval
            </div>
          </div>

          <div className="min-h-full w-1/2 rounded border p-4">
            <div className="mb-2 text-grayTen">Last Updated</div>
            {(() => {
              // Use the most recent dateModified from activity log, fallback to doc.createdAt
              const lastUpdatedDate =
                activityLog.length > 0
                  ? activityLog[0].dateModified // Activity log is already sorted by most recent first
                  : doc.createdAt;

              return (
                lastUpdatedDate && (
                  <div
                    className={`flex w-fit items-center gap-2 rounded-full border border-grayTen px-5 py-2.5 text-xs font-medium text-grayTen`}
                  >
                    {moment(lastUpdatedDate).format('LL')}
                  </div>
                )
              );
            })()}
          </div>
        </div>
      )}

      {/* Overview Section */}
      <div className="rounded border p-4">
        <div className="text-lg font-bold">Overview</div>
        <p className="mt-2.5 font-medium">{doc.description}</p>
      </div>

      {/* Document Section */}
      {hasFile && doc.fileName && (
        <div className="rounded border p-4">
          <div className="mb-3 text-lg font-bold">Document</div>
          <div className="flex items-center gap-3">
            <div
              className="group relative cursor-pointer"
              onClick={handleDownload}
              title={doc.url ? 'Download file' : 'File not available'}
            >
              {/* File Preview */}
              <FilePreview
                url={doc.url}
                fileName={doc.fileName}
                className="h-20 w-16 rounded border bg-white shadow-sm"
              />

              {doc.url && (
                <div className="absolute inset-0 flex items-center justify-center rounded bg-black/20 opacity-0 transition-opacity group-hover:opacity-100">
                  <Download className="h-6 w-6 text-white" />
                </div>
              )}
            </div>

            <div className="text-grayTen">
              <p className="font-medium">{doc.fileName}</p>
              {doc.size && (
                <p className="text-sm">{formatFileSize(doc.size)}</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Activity Log Section */}
      {hasFile && (
        <div className="rounded border p-4">
          <div className="mb-3 text-lg font-bold">Activity Log</div>
          {isActivityLogLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
              <span className="ml-3 text-sm text-gray-600">
                Loading activity log...
              </span>
            </div>
          ) : activityLog.length > 0 ? (
            <div className="space-y-3">
              {(showAllActivities ? activityLog : activityLog.slice(0, 3)).map(
                (activity, index) => (
                  <div key={index} className="flex items-center gap-4">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect
                        x="5.5"
                        y="5.5"
                        width="21"
                        height="21"
                        rx="10.5"
                        fill="white"
                      />
                      <rect
                        x="5.5"
                        y="5.5"
                        width="21"
                        height="21"
                        rx="10.5"
                        stroke="#FF3E00"
                        strokeWidth="11"
                      />
                    </svg>

                    <div>
                      <div className="mb-1.5 text-sm text-gray-400">
                        {moment(activity.dateModified).format('LLL')}
                      </div>
                      <div className="text-sm">{`${activity.modifiedByFirstName ?? ''} ${activity.modifiedByLastName ?? ''} ${activity.action.toLocaleLowerCase()} the document`}</div>
                    </div>
                  </div>
                )
              )}

              {/* Load More Button */}
              {activityLog.length > 3 && !showAllActivities && (
                <div className="flex justify-center pt-3">
                  <button
                    onClick={() => setShowAllActivities(true)}
                    className="rounded-md border border-primary px-4 py-2 text-sm font-medium text-primary transition-colors hover:bg-primary hover:text-white"
                  >
                    Load more
                  </button>
                </div>
              )}
            </div>
          ) : (
            <p className="text-sm text-gray-400">No activity log available</p>
          )}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <AnimatedModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Delete Document"
        maxWidth="md"
      >
        <div className="flex flex-col items-center justify-center space-y-6">
          <img src={alertIcon} alt="" className="h-16 w-16" />
          <div className="text-center font-medium text-subText">
            You're going to delete the{' '}
            <span className="font-bold">"{doc.name}"</span> document.
          </div>

          <div className="flex gap-6 pt-4">
            <button
              onClick={() => setDeleteModalOpen(false)}
              className="rounded-lg bg-grayThirteen px-6 py-3 text-sm font-medium text-white transition-colors hover:bg-grayTwentyOne"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className="flex items-center gap-2 rounded-lg bg-red-600 px-6 py-3 text-sm font-medium text-white transition-colors hover:bg-red-700"
            >
              <Trash2 className="h-4 w-4" />
              Delete
            </button>
          </div>
        </div>
      </AnimatedModal>

      {/* Upload Modal */}
      <KnowledgeBaseFileUploadModal
        multiple={false}
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        title={doc.name}
        action={hasFile ? 'Replace' : 'Upload'}
        onFilesUpload={handleUpload}
        currentAgent={
          selectedLevel === 'agent'
            ? allAgents
                .map(a => ({
                  id: a.id,
                  name: a.name,
                  icon: a.icon,
                }))
                .find(a => a.id === selectedAgent)
            : {
                id: selectedSuite?.agentSuiteKey,
                name: selectedSuite?.agentSuiteName,
                icon: selectedSuite?.avatar,
              }
        }
      />
    </div>
  );
};

export default DocumentDetails;
