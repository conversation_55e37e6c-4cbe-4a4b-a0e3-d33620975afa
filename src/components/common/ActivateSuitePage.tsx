import { useQueryClient } from '@tanstack/react-query';
import { ChevronLeft, Plus } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { Spinner } from '@/components/common/Loader';
import { NotificationContainer } from '@/components/ui';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { agentSuites as mockAgentsSuite } from '@/data/constants';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import { useNotifications } from '@/hooks/useNotifications';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { AgentCard } from '@/pages/AiAgentsPage';
import { useClaimAgentSuiteApi } from '@/services/upivotalAgenticService';
import { AIAgent } from '@/types/agents';
import { UserBasicInfoPayload } from '@/types/user';
import { GET_USER_QUERY } from '@/utils/queryKeys';

export type PageType =
  | 'ai-agents'
  | 'business-stack'
  | 'analytics'
  | 'knowledge-base';

interface ActivateSuitePageProps {
  pageType: PageType;
  suiteId: string;
}

const ActivateSuitePage: React.FC<ActivateSuitePageProps> = ({
  pageType,
  suiteId,
}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Initialize notification system
  const { notifyWithImage, notifications, dismiss } = useNotifications();

  // Primary data flow: API-based approach
  const { data: agentSuites = [], isLoading: isLoadingSuites } =
    useGetAIAgentSuites();
  const suite = agentSuites.find(s => s.agentSuiteKey === suiteId);

  // Get user data to check if suite is already claimed
  const { data: userData, isLoading: isLoadingUser } =
    useGetUserProfile<UserBasicInfoPayload>();

  // Use API data only - no longer relying on location.state
  const finalSuite = suite;

  // Get agents that belong to this suite
  const suiteAgents: AIAgent[] = finalSuite?.availableAgents || [];
  const suiteFallbackImage = mockAgentsSuite.filter(
    mockAgent =>
      mockAgent.id.toLowerCase() === finalSuite?.agentSuiteKey.toLowerCase()
  )[0]?.image;

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [chatMessage, setChatMessage] = useState<string>('');
  const { activeAgent, setActiveAgent, setTenantId } = useTenant();

  const claimAgentsSuite = useClaimAgentSuiteApi();
  const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites;

  const isMatchingSuite = useMemo(() => {
    return claimedSuites?.some(
      claimedSuite => claimedSuite.suite.agentSuiteKey === suiteId
    );
  }, [claimedSuites, suiteId]);
  // Check if current user is a member of this suite and get user role
  const currentUserSuiteInfo = claimedSuites?.find(claimedSuite => {
    const isUserMember = claimedSuite.members.some(
      member => member?.user?.userId === userData?.userInfo?.userId
    );
    return isUserMember;
  });

  const isUserMember = !!currentUserSuiteInfo;

  // Get current user's role in this suite
  const currentUserRole =
    currentUserSuiteInfo?.members?.find(
      member => member?.user?.userId === userData?.userInfo?.userId
    )?.memberRoles?.[0] || 'member';
  // Check if user can manage members (manager or lead roles)
  const canManageMembers =
    currentUserRole?.toLowerCase() === 'manager' ||
    currentUserRole?.toLowerCase() === 'lead';

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
  };

  // Dynamic navigation based on pageType
  const getSuccessRoute = () => {
    switch (pageType) {
      case 'business-stack':
        return ROUTES.DASHBOARD_BUSINESS_STACK;
      case 'analytics':
        return `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS}`;
      case 'ai-agents':
      case 'knowledge-base':
        return ROUTES.DASHBOARD_KNOWLEDGE_BASE;
      default:
        return ROUTES.DASHBOARD_BASE;
    }
  };

  // Dynamic breadcrumb configuration
  const getBreadcrumbConfig = () => {
    switch (pageType) {
      case 'ai-agents':
        return {
          text: 'Agents Suites',
          action: () => navigate(ROUTES.DASHBOARD_AI_AGENTS),
        };
      case 'knowledge-base':
        return {
          text: 'Knowledge Base',
          action: () => navigate(-1),
        };
      default:
        return {
          text: 'Agents Suites',
          action: () => navigate(-1),
        };
    }
  };

  const handleClaimAgentSuite = async (agentSuiteKey: string) => {
    try {
      setIsLoading(true);
      setChatMessage('');

      const response = await claimAgentsSuite(agentSuiteKey);

      if (response.status === true) {
        // Set the tenant ID from the response
        setTenantId(response.data.tenantId);

        const message =
          response.message || 'Agents suite claimed successfully!';

        if (finalSuite?.avatar) {
          notifyWithImage(
            message,
            finalSuite.avatar,
            'h-8 w-8 rounded object-contain'
          );
        } else {
          notifyWithImage(message, '', '');
        }

        // Invalidate user profile queries to refresh claimed suites data
        await queryClient.invalidateQueries({ queryKey: [GET_USER_QUERY] });

        // Force refetch with a slight delay to ensure backend consistency
        setTimeout(async () => {
          await queryClient.refetchQueries({ queryKey: [GET_USER_QUERY] });
        }, 1500);

        navigate(getSuccessRoute());
      } else {
        const errorMessage = response.message || 'Failed to claim agents suite';
        console.error('[ActivateSuitePage] Suite claim failed:', errorMessage);
        notifyWithImage(errorMessage, '', '');
      }
    } catch (error: unknown) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'response' in error
          ? (error as any).response?.data?.message
          : error instanceof Error
            ? error.message
            : 'Failed to claim agents suite. Please try again.';

      console.error('[ActivateSuitePage] Suite claim error:', errorMessage);
      // notifyWithImage(errorMessage, '', '');
    } finally {
      setIsLoading(false);
    }
  };

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  useEffect(() => {
    if (!activeAgent) return;

    const reloadForAgent = async () => {
      try {
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }
      } catch (error) {
        console.error('Error occured while changing agent:', error);
      }
    };

    reloadForAgent();
  }, [activeAgent]);

  // Show loading state while fetching data
  if (isLoadingSuites || isLoadingUser) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Spinner className="mx-auto mb-4 h-8 w-8" />
          <p className="text-lg text-gray-600">Loading agents suite...</p>
        </div>
      </div>
    );
  }

  // Show error state if suite not found
  if (!finalSuite) {
    const breadcrumbConfig = getBreadcrumbConfig();
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Agents Suite Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The agents suite with ID "{suiteId}" could not be found.
          </p>
          <button
            onClick={breadcrumbConfig.action}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            {pageType === 'ai-agents' ? 'Back to Agents Hub' : 'Go Back'}
          </button>
        </div>
      </div>
    );
  }

  const breadcrumbConfig = getBreadcrumbConfig();

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalInjectedMessage={chatMessage}
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex w-full max-w-[850px] flex-col gap-4 p-8">
          <div className="flex items-center justify-between gap-4">
            {/* Breadcrumb */}
            {pageType === 'ai-agents' ? (
              <Link
                to={ROUTES.DASHBOARD_AI_AGENTS}
                className="flex items-center gap-1 font-semibold text-blackTwo"
              >
                <ChevronLeft
                  className="h-4 w-4 sm:h-6 sm:w-6"
                  strokeWidth={2}
                />
                {breadcrumbConfig.text}
              </Link>
            ) : (
              <button
                onClick={breadcrumbConfig.action}
                className="flex items-center gap-1 font-semibold text-blackTwo"
              >
                <ChevronLeft
                  className="h-4 w-4 sm:h-6 sm:w-6"
                  strokeWidth={2}
                />
                {breadcrumbConfig.text}
              </button>
            )}
            {isUserMember && canManageMembers && (
              <button
                onClick={() =>
                  navigate(ROUTES.DASHBOARD_SETTINGS_MEMBERS_INVITE, {
                    state: {
                      agentSuiteKey: finalSuite.agentSuiteKey,
                      returnParams: '?tab=invite',
                    },
                  })
                }
                className="flex h-[42px] items-center gap-2 rounded-md bg-primary px-4 text-sm font-normal text-white transition-colors hover:bg-primary/90"
              >
                <Plus className="h-4 w-4" strokeWidth={3} /> Add Members
              </button>
            )}
          </div>

          {/* Notifications Container */}
          <NotificationContainer
            notifications={notifications}
            onClose={dismiss}
            className="w-full"
            maxNotifications={3}
          />

          {!isMatchingSuite && (
            <div className="mb-2 flex items-center justify-between rounded-xl border border-primary bg-peachTwo p-5 lg:h-[116px]">
              <div className="flex items-center gap-2">
                <div className="flex h-[46px] w-[46px] shrink-0 items-center justify-center gap-2 rounded-full border border-primary">
                  <Icons.ClaimIcon className="h-[24px] w-[18px]" />
                </div>
                <div>
                  <div className="font-spartan text-base font-medium text-blackOne">
                    Claim Your Suite
                  </div>
                  <p className="mt-1 font-inter text-sm text-subText">
                    Start using your AI agents immediately by claiming this
                    suite
                  </p>
                </div>
              </div>
              <button
                className="rounded bg-primary px-4 py-3 font-normal text-white transition-colors hover:bg-orange-15 disabled:opacity-30"
                onClick={() =>
                  finalSuite && handleClaimAgentSuite(finalSuite.agentSuiteKey)
                }
                disabled={isLoading || !finalSuite}
              >
                <div className="flex items-center gap-2">
                  {isLoading && (
                    <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                  )}
                  <span>Claim this suite</span>
                </div>
              </button>
            </div>
          )}

          {isUserMember && !canManageMembers && (
            <div className="mb-2 flex items-center justify-between rounded-xl border border-primary bg-peachTwo p-5 lg:h-[116px]">
              <div className="flex items-center gap-2">
                <div className="flex h-[46px] w-[46px] shrink-0 items-center justify-center gap-2 rounded-full border border-primary">
                  <svg
                    className="h-[24px] w-[24px] text-primary"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div>
                  <div className="font-spartan text-base font-medium text-blackOne">
                    Suite activated
                  </div>
                  <p className="mt-1 font-inter text-sm text-subText">
                    Activate your AI collections team — faster resolutions and
                    results from day one.
                  </p>
                </div>
              </div>
              <div className="rounded-lg bg-blackOne px-4 py-3 text-sm font-medium text-white">
                Suite activated
              </div>
            </div>
          )}
          {/* Suite Header */}
          <div className="relative h-[198px] overflow-hidden rounded-xl bg-cover bg-center font-spartan">
            {/* Background Image */}
            <div
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: finalSuite.avatar
                  ? `url(${finalSuite.avatar})`
                  : `url(${suiteFallbackImage})`,
              }}
            />
            {/* Dark Overlay */}
            <div className="absolute inset-0 bg-black/20" />
            {/* Content */}
            <div className="relative z-10 flex h-full flex-col justify-center p-6">
              <div className="flex w-fit items-center justify-center rounded-lg bg-white px-4 py-3">
                <h1 className="mt-1 text-[32px] font-bold leading-none">
                  {finalSuite.agentSuiteName}
                </h1>
              </div>
              <h2 className="mt-8 w-fit text-[20px] font-semibold text-white">
                {finalSuite.description}
              </h2>
              <div className="font-inter text-lg text-white">
                {finalSuite.roleDescription}
              </div>
            </div>
          </div>

          {/* Agents Grid */}
          {suiteAgents.length > 0 && (
            <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
              {suiteAgents.map(agent => (
                <AgentCard
                  key={agent.agentKey}
                  agent={agent}
                  showChatButton={true}
                  isActiveAgent={activeAgent === agent.agentKey}
                  link={
                    agent.agentKey
                      ? ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(agent.agentKey)
                      : '#'
                  }
                  onAgentSelect={() => handleAgentSelect(agent.agentKey)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActivateSuitePage;
