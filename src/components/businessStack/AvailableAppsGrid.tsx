import { ChevronDown, Search } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { Spinner } from '@/components/common/Loader';
import { Button } from '@/components/ui';
import { useDebounce } from '@/hooks/useDebounce';

import { useAppCategoriesApi } from '../../services/businessStackService';
import { AvailableAppsGridProps } from '../../types/businessStack';
import { AppCard } from './AppCard';

export const AvailableAppsGrid: React.FC<AvailableAppsGridProps> = ({
  apps,
  isLoading,
  searchQuery,
  selectedCategory,
  onSearchChange,
  onCategoryChange,
  onConnectApp,
  connectionFlow,
  onOpenTwilioModal,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [categories, setCategories] = useState<
    Array<{ categoryName: string; categoryAlias: string }>
  >([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);

  const getAppCategories = useAppCategoriesApi();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showCategoryDropdown && !target.closest('.category-dropdown')) {
        setShowCategoryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showCategoryDropdown]);

  const debouncedSearchQuery = useDebounce(localSearchQuery, 1000);

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setIsLoadingCategories(true);
        const response = await getAppCategories();
        if (response.status && response.data) {
          setCategories(response.data);
        }
      } catch (error) {
        console.error('Error loading app categories:', error);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  // Sync local state when parent searchQuery changes (e.g., when parent resets search)
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // Trigger API call when debounced value changes
  useEffect(() => {
    // Only call onSearchChange if the debounced value is different from current searchQuery
    if (debouncedSearchQuery !== searchQuery) {
      onSearchChange(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, onSearchChange, searchQuery]);

  // Handle input change - update local state immediately for responsive UI
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };

  // Handle category selection
  const handleCategorySelect = (categoryName: string) => {
    onCategoryChange(categoryName);
    setShowCategoryDropdown(false);
  };

  // Get selected category display name
  const getSelectedCategoryDisplay = () => {
    if (!selectedCategory) return 'All Categories';
    const category = categories.find(
      cat => cat.categoryName === selectedCategory
    );
    return category ? category.categoryAlias : 'All Categories';
  };

  // Sort apps alphabetically and filter based on search query
  const filteredApps = apps
    .sort((a, b) => a.name.localeCompare(b.name))
    .filter(app => app.name.toLowerCase().includes(searchQuery.toLowerCase()));

  return (
    <div className="flex h-full flex-col">
      <div className="mb-6 flex justify-between gap-4">
        {/* Search Bar */}
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400" />
          <input
            type="text"
            placeholder="Search"
            value={localSearchQuery}
            onChange={handleSearchInputChange}
            className="w-full rounded-lg border border-gray-300 py-3 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
          />
        </div>

        {/* Category Filter */}
        <div className="category-dropdown relative">
          <Button
            onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
            className="flex h-[46px] min-w-[140px] items-center gap-2 rounded-[10px] border border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:bg-primary hover:text-white"
          >
            <span className="truncate">{getSelectedCategoryDisplay()}</span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`}
            />
          </Button>

          {showCategoryDropdown && (
            <div className="absolute right-0 top-full z-10 mt-1 w-64 rounded-lg border border-gray-200 bg-white shadow-lg">
              <div className="max-h-60 overflow-y-auto py-1">
                <button
                  onClick={() => handleCategorySelect('')}
                  className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-50 ${
                    !selectedCategory
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-700'
                  }`}
                >
                  All Categories
                </button>
                {categories.map(category => (
                  <button
                    key={category.categoryName}
                    onClick={() => handleCategorySelect(category.categoryName)}
                    className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-50 ${
                      selectedCategory === category.categoryName
                        ? 'bg-primary/10 text-primary'
                        : 'text-gray-700'
                    }`}
                  >
                    {category.categoryAlias}
                  </button>
                ))}
                {isLoadingCategories && (
                  <div className="px-4 py-2 text-sm text-gray-500">
                    Loading categories...
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add notifications container here  */}

      {/* Apps Grid */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex h-full items-center justify-center py-24">
            <div className="flex flex-col items-center gap-3">
              <Spinner />
              <p className="text-sm text-gray-500">Loading available apps...</p>
            </div>
          </div>
        ) : filteredApps.length === 0 ? (
          <div className="flex h-full items-center justify-center py-20">
            <div className="text-center">
              <div className="mb-4 text-4xl">🔍</div>
              <h3 className="mb-2 text-lg font-medium text-blackOne">
                {searchQuery ? 'No apps found' : 'No apps available'}
              </h3>
              <p className="text-sm text-gray-500">
                {searchQuery
                  ? `No apps match "${searchQuery}". Try a different search term.`
                  : 'There are no available apps to display at the moment.'}
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-x-4 gap-y-10 sm:grid-cols-2 lg:grid-cols-4">
            {filteredApps.map(app => (
              <AppCard
                key={app.key}
                app={app}
                onConnect={onConnectApp}
                connectionFlow={connectionFlow}
                onOpenTwilioModal={onOpenTwilioModal}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
