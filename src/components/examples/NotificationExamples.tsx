import React from 'react';

import { Button } from '@/components/ui/Button';
import {
  NotificationContainer,
  NotificationProvider,
  useNotifications,
} from '@/components/ui/notifications';

/**
 * Example component demonstrating how to use the notification toast system
 *
 * This component shows various usage patterns including:
 * - Simple notifications with default agent
 * - Notifications with additional images/logos
 * - Notifications with custom agent overrides
 * - Multiple simultaneous notifications
 * - Form validation and error handling
 */
const NotificationExamplesContent: React.FC = () => {
  const {
    notify,
    notifyWithImage,
    notifyWithAgent,
    notifyCustom,
    dismissAll,
    dismiss,
    notifications,
    hasNotifications,
    notificationCount,
    isLoading,
  } = useNotifications();

  // Example notification messages from the requirements
  const handleSalesforceConnection = () => {
    console.log(
      '[NotificationExamples] Triggering Salesforce connection notification'
    );
    notifyWithImage(
      'I have successfully connected to Salesforce',
      'https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg',
      'h-8 w-8 object-contain'
    );
  };

  const handleEmailRemoval = () => {
    console.log('[NotificationExamples] Triggering email removal notification');
    notify('<EMAIL> has been removed from SetIQ suites.');
  };

  const handleSuiteClaimed = () => {
    console.log('[NotificationExamples] Triggering suite claimed notification');
    notifyWithImage(
      'SetIQ suite claimed',
      '/src/assets/images/SetIQ.svg',
      'h-8 w-8 rounded object-contain'
    );
  };

  const handleCustomAgentNotification = () => {
    console.log('[NotificationExamples] Triggering custom agent notification');
    notifyWithAgent(
      'Custom agent notification example',
      'Colton',
      '/src/assets/images/colton.png'
    );
  };

  const handleMultipleNotifications = () => {
    console.log('[NotificationExamples] Triggering multiple notifications');

    // Add multiple notifications to test stacking
    notify('First notification - newest will be on top');

    setTimeout(() => {
      notifyWithImage(
        'Second notification with image',
        'https://via.placeholder.com/32x32/FF3E00/FFFFFF?text=2',
        'h-8 w-8 rounded-full'
      );
    }, 500);

    setTimeout(() => {
      notifyWithAgent(
        'Third notification with custom agent',
        'Scyra',
        '/src/assets/images/scyra.png'
      );
    }, 1000);
  };

  const handleFormValidationExample = () => {
    console.log('[NotificationExamples] Testing form validation');

    try {
      // This should trigger validation error
      notify('');
    } catch (error) {
      console.error('Validation error caught:', error);
      notify('Form validation works! Empty messages are not allowed.');
    }
  };

  const handleLoadingStateExample = () => {
    console.log('[NotificationExamples] Testing loading states');

    // Add a notification that demonstrates loading state
    notifyCustom({
      message: 'Processing your request...',
      additionalImage:
        'https://via.placeholder.com/32x32/FFA500/FFFFFF?text=⏳',
      additionalImageClassname: 'h-8 w-8 animate-spin',
    });

    // Simulate completion after 3 seconds
    setTimeout(() => {
      notify('Request completed successfully!');
    }, 3000);
  };

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-blackOne">
          Notification Toast Examples
        </h2>
        <p className="text-gray-600">
          Click the buttons below to test different notification scenarios.
          Notifications will appear inline below this section.
        </p>
      </div>

      {/* Control Buttons */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Button onClick={handleSalesforceConnection} className="w-full">
          Salesforce Connected
        </Button>

        <Button
          onClick={handleEmailRemoval}
          variant="outline"
          className="w-full"
        >
          Email Removed
        </Button>

        <Button
          onClick={handleSuiteClaimed}
          variant="secondary"
          className="w-full"
        >
          Suite Claimed
        </Button>

        <Button
          onClick={handleCustomAgentNotification}
          variant="ghost"
          className="w-full"
        >
          Custom Agent
        </Button>

        <Button onClick={handleMultipleNotifications} className="w-full">
          Multiple Notifications
        </Button>

        <Button
          onClick={handleFormValidationExample}
          variant="outline"
          className="w-full"
        >
          Test Validation
        </Button>

        <Button
          onClick={handleLoadingStateExample}
          variant="secondary"
          className="w-full"
        >
          Loading State
        </Button>

        <Button
          onClick={dismissAll}
          variant="destructive"
          className="w-full"
          disabled={!hasNotifications}
        >
          Clear All ({notificationCount})
        </Button>
      </div>

      {/* Status Information */}
      <div className="rounded-lg bg-gray-50 p-4">
        <h3 className="mb-2 font-medium text-blackOne">Status</h3>
        <div className="space-y-1 text-sm text-gray-600">
          <p>Active Notifications: {notificationCount}</p>
          <p>Has Notifications: {hasNotifications ? 'Yes' : 'No'}</p>
          <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
        </div>
      </div>

      {/* Notification Container - This is where notifications will appear */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-blackOne">
          Notifications ({notificationCount})
        </h3>
        <NotificationContainer
          notifications={notifications}
          onClose={dismiss}
          className="w-full"
          maxNotifications={5}
        />
        {!hasNotifications && (
          <p className="italic text-gray-500">No notifications to display</p>
        )}
      </div>
    </div>
  );
};

/**
 * Main example component with NotificationProvider wrapper
 * This shows the complete setup including the provider
 */
const NotificationExamples: React.FC = () => {
  return (
    <NotificationProvider maxNotifications={5}>
      <NotificationExamplesContent />
    </NotificationProvider>
  );
};

export default NotificationExamples;
