import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import React, { useCallback } from 'react';

import { useTenant } from '@/context/TenantContext';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { cn } from '@/lib/twMerge/cn';
import { NotificationToastProps } from '@/types/notifications';
import { UserBasicInfoPayload } from '@/types/user';
import { getAgentAvatar, getAgentName } from '@/utils/agentUtils';

/**
 * Individual notification toast component
 */
const NotificationToast = React.forwardRef<
  HTMLDivElement,
  NotificationToastProps
>(({ notification, onClose, className, size = 'large' }, ref) => {
  const { activeAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  // Determine which agent to use - custom override or active agent
  const agentToUse = notification.activeAgent || {
    agentName: getAgentName(activeAgent, userData),
    avatar: getAgentAvatar(activeAgent, userData),
  };

  // Handle close button click
  const handleClose = useCallback(() => {
    onClose(notification.id);
  }, [notification.id, onClose]);

  // Handle keyboard navigation for close button
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        handleClose();
      }
    },
    [handleClose]
  );

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: -20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{
        duration: 0.3,
        ease: [0.4, 0.0, 0.2, 1],
      }}
      className={cn(
        // Base container styles
        'flex items-center gap-3 rounded-lg border border-primary bg-peach-5 p-4 shadow-sm',
        // Hover and focus states
        'transition-shadow duration-200 hover:shadow-md',
        // Responsive spacing
        'min-h-[60px] w-full max-w-2xl',
        size === 'small' && 'p-2',
        className
      )}
      role="alert"
      aria-live="polite"
      aria-label={`Notification from ${agentToUse.agentName}: ${notification.message}`}
    >
      <div
        className={`flex w-full justify-between rounded bg-white py-2 ${size === 'large' && 'items-center px-2'}`}
      >
        <div
          className={`flex items-center gap-3 ${size === 'small' ? 'flex-col' : 'flex-row'}`}
        >
          <div className="flex items-center gap-2">
            {/* Agent Avatar */}
            <div className="flex-shrink-0">
              <div className="h-10 w-10 overflow-hidden rounded-full bg-peachTwo">
                <img
                  src={agentToUse.avatar}
                  alt={`${agentToUse.agentName} avatar`}
                  className="h-full w-full object-cover"
                  onError={e => {
                    console.warn(
                      `[NotificationToast] Failed to load avatar for ${agentToUse.agentName}`
                    );
                    // Fallback to a default background color if image fails
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              </div>
            </div>

            {/* Agent Name */}
            <div className="flex-shrink-0">
              <span className="text-sm font-medium text-blackOne">
                {agentToUse.agentName}
              </span>
            </div>
          </div>

          {/* Message */}
          <div className="min-w-0 flex-1 text-center">
            <span className="text-sm text-gray-700">
              {notification.message}
            </span>
          </div>

          {/* Optional Additional Image */}
          {notification.additionalImage && (
            <div className="flex-shrink-0">
              <img
                src={notification.additionalImage}
                alt="Additional notification image"
                className={cn(
                  // Default styling for additional image
                  'h-8 w-8 object-contain',
                  // Apply custom classes if provided
                  notification.additionalImageClassname
                )}
                onError={e => {
                  console.warn(
                    `[NotificationToast] Failed to load additional image:`,
                    notification.additionalImage
                  );
                  // Hide the image if it fails to load
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          )}
        </div>

        {/* Close Button */}
        <div className="flex-shrink-0">
          <button
            onClick={handleClose}
            onKeyDown={handleKeyDown}
            className={cn(
              // Base button styles
              'flex h-6 w-6 items-center justify-center rounded-full',
              // Interactive states
              'hover:bg-gray-100 focus:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1',
              // Transition
              'transition-colors duration-150'
            )}
            aria-label={`Close notification from ${agentToUse.agentName}`}
            title="Close notification"
            type="button"
          >
            <X
              className="h-4 w-4 text-gray-500 hover:text-gray-700"
              strokeWidth={2}
            />
          </button>
        </div>
      </div>
    </motion.div>
  );
});

// Add display name for debugging
NotificationToast.displayName = 'NotificationToast';

export default NotificationToast;
