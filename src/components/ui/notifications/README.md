# Notification Toast System

A reusable React notification toast component system that displays inline notifications between other elements in the app. Supports multiple simultaneous notifications stacked vertically with the newest on top.

## Features

- **Inline Display**: Notifications appear between page elements, not as overlays
- **Multiple Notifications**: Support for simultaneous notifications with vertical stacking
- **Agent Integration**: Automatic integration with `useTenant` context for agent information
- **Custom Agents**: Override default agent with custom name and avatar
- **Additional Images**: Support for service logos and additional images
- **Accessibility**: Full ARIA support, keyboard navigation, and screen reader compatibility
- **Form Validation**: Built-in validation for required fields
- **Loading States**: Progress indicators and loading state management
- **TypeScript**: Complete TypeScript support with comprehensive interfaces
- **Animations**: Smooth enter/exit animations using Framer Motion
- **Console Logging**: Development-friendly logging for debugging

## Component Structure

Each notification follows a left-to-right layout:
```
[Agent Avatar] [Agent Name] "[Message]" [Optional Image] [X Close Button]
```

### Visual Examples
- `[Scyra avatar] Scyra "I have successfully connected to Salesforce" [Salesforce logo] X`
- `[Regis avatar] Regis "<EMAIL> has been removed from SetIQ suites." X`
- `[Colton avatar] Colton "SetIQ suite claimed" [SetIQ avatar] X`

## Quick Start

### 1. Setup Provider

Wrap your app with the `NotificationProvider`:

```tsx
import { NotificationProvider, NotificationContainer } from '@/components/ui/notifications';

function App() {
  return (
    <NotificationProvider maxNotifications={5}>
      <YourAppContent />
      {/* Place NotificationContainer where you want notifications to appear */}
      <NotificationContainer />
    </NotificationProvider>
  );
}
```

### 2. Use in Components

```tsx
import { useNotifications } from '@/components/ui/notifications';

function MyComponent() {
  const { notify, notifyWithImage, notifyWithAgent } = useNotifications();

  const handleSuccess = () => {
    notify("I have successfully connected to Salesforce");
  };

  const handleWithLogo = () => {
    notifyWithImage(
      "SetIQ suite claimed", 
      "/path/to/setiq-logo.png",
      "h-6 w-6 rounded"
    );
  };

  const handleCustomAgent = () => {
    notifyWithAgent(
      "Custom message",
      "Colton",
      "/path/to/colton-avatar.png"
    );
  };

  return (
    <div>
      <button onClick={handleSuccess}>Show Success</button>
      <button onClick={handleWithLogo}>Show With Logo</button>
      <button onClick={handleCustomAgent}>Show Custom Agent</button>
    </div>
  );
}
```

## API Reference

### NotificationProvider Props

```tsx
interface NotificationProviderProps {
  children: React.ReactNode;
  maxNotifications?: number; // Default: 5
  autoRemoveDelay?: number;  // Default: 0 (no auto-removal)
}
```

### useNotifications Hook

```tsx
const {
  // Simple methods
  notify,                    // (message: string) => string
  notifyWithImage,          // (message, imageUrl, className?) => string
  notifyWithAgent,          // (message, agentName, avatar) => string
  notifyCustom,             // (options: AddNotificationOptions) => string
  
  // Management
  dismiss,                  // (id: string) => void
  dismissAll,              // () => void
  
  // State
  notifications,           // NotificationData[]
  hasNotifications,        // boolean
  notificationCount,       // number
  isLoading,              // boolean
} = useNotifications();
```

### Required Props

```tsx
interface AddNotificationOptions {
  message: string;                    // Required - notification text
  additionalImage?: string;           // Optional - URL for logo/image
  additionalImageClassname?: string;  // Optional - Tailwind classes
  activeAgent?: {                     // Optional - custom agent override
    agentName: string;
    avatar: string;
  };
}
```

## Advanced Usage

### Custom Styling

```tsx
// Custom container styling
<NotificationContainer 
  className="fixed top-4 right-4 z-50 w-96"
  maxNotifications={3}
/>

// Custom notification styling with additional image
notifyWithImage(
  "Connection successful",
  "/salesforce-logo.svg",
  "h-8 w-8 rounded-full border-2 border-green-500"
);
```

### Form Validation Example

```tsx
const handleSubmit = async (formData) => {
  try {
    setIsLoading(true);
    
    // Validate required fields
    if (!formData.email) {
      notify("Email is required");
      return;
    }
    
    const result = await submitForm(formData);
    
    // Success notification with service logo
    notifyWithImage(
      "Form submitted successfully",
      "/success-icon.svg",
      "h-6 w-6 text-green-500"
    );
    
  } catch (error) {
    notify(`Error: ${error.message}`);
  } finally {
    setIsLoading(false);
  }
};
```

### Loading States

```tsx
const handleAsyncOperation = async () => {
  // Show loading notification
  const loadingId = notifyCustom({
    message: "Processing your request...",
    additionalImage: "/loading-spinner.svg",
    additionalImageClassname: "h-6 w-6 animate-spin"
  });
  
  try {
    await performOperation();
    
    // Remove loading notification
    dismiss(loadingId);
    
    // Show success
    notify("Operation completed successfully!");
    
  } catch (error) {
    dismiss(loadingId);
    notify(`Operation failed: ${error.message}`);
  }
};
```

## Accessibility Features

- **ARIA Labels**: Proper `role="alert"` and `aria-live="polite"`
- **Keyboard Navigation**: Close buttons support Enter and Space keys
- **Screen Reader**: Announcements for notification count changes
- **Focus Management**: Proper focus handling for interactive elements

## TypeScript Support

All components and hooks are fully typed with comprehensive interfaces:

```tsx
import type { 
  NotificationData,
  AddNotificationOptions,
  NotificationAgent 
} from '@/components/ui/notifications';
```

## Styling Guidelines

The component uses Tailwind CSS classes and follows the app's design system:

- **Colors**: Uses `blackOne`, `peachTwo`, `primary` from the design system
- **Spacing**: Consistent with existing component spacing patterns
- **Typography**: Matches app typography scale
- **Animations**: Smooth transitions using Framer Motion

## Development

### Console Logging

All notification actions are logged to the console during development:

```
[NotificationProvider] Adding notification: { id: "...", message: "..." }
[NotificationToast] Rendering notification: { id: "...", agent: "Scyra" }
[NotificationProvider] Removing notification: notification_123456
```

### Testing

Use the `NotificationExamples` component to test all functionality:

```tsx
import NotificationExamples from '@/components/examples/NotificationExamples';

// Render in your development environment
<NotificationExamples />
```

## Integration with Existing Codebase

The notification system integrates seamlessly with:

- **TenantContext**: Automatically uses `activeAgent` from `useTenant()`
- **Agent Utils**: Uses `getAgentAvatar()` and `getAgentName()` utilities
- **Design System**: Follows existing color and spacing patterns
- **TypeScript**: Zero TypeScript errors with comprehensive type coverage
