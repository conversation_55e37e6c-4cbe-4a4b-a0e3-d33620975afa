import { AnimatePresence } from 'framer-motion';
import React, { useEffect, useRef } from 'react';

import { useNotificationContext } from '@/context/NotificationContext';
import { cn } from '@/lib/twMerge/cn';
import { NotificationContainerProps } from '@/types/notifications';

import NotificationToast from './NotificationToast';

/**
 * Container component for managing multiple notification toasts
 * Automatically clears notifications on mount and unmount for page-specific behavior
 */
const NotificationContainer: React.FC<NotificationContainerProps> = ({
  notifications,
  onClose,
  className,
  maxNotifications = 5,
  size = 'large',
}) => {
  const { clearNotifications } = useNotificationContext();
  const isInitialMount = useRef(true);

  // Auto-clear notifications when component mounts (user enters page)
  // and when component unmounts (user leaves page)
  useEffect(() => {
    // Clear notifications only on initial mount if there are existing notifications
    if (isInitialMount.current && notifications.length > 0) {
      console.log(
        '[NotificationContainer] Clearing existing notifications on page entry'
      );
      clearNotifications();
    }
    isInitialMount.current = false;

    // Return cleanup function to clear notifications when leaving the page
    return () => {
      console.log(
        '[NotificationContainer] Clearing notifications on page exit'
      );
      clearNotifications();
    };
  }, [clearNotifications, notifications.length]); // Include notifications.length for the initial check

  // Filter out notifications that are being removed and limit display count
  const visibleNotifications = notifications
    .filter(notification => !notification.isRemoving)
    .slice(0, maxNotifications);

  // Don't render container if no notifications
  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div
      className={cn(
        // Base container styles
        'w-full space-y-3',
        'max-w-2xl',
        className
      )}
      role="region"
      aria-label="Notifications"
      aria-live="polite"
    >
      <AnimatePresence mode="popLayout">
        {visibleNotifications.map(notification => (
          <NotificationToast
            key={notification.id}
            notification={notification}
            onClose={onClose}
            className="w-full"
            size={size}
          />
        ))}
      </AnimatePresence>

      {/* Screen reader announcement for notification count */}
      <div className="sr-only" aria-live="polite">
        {visibleNotifications.length === 1
          ? '1 notification'
          : `${visibleNotifications.length} notifications`}
      </div>
    </div>
  );
};

export default NotificationContainer;
